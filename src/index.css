@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@import "tailwindcss";
@reference "tailwindcss";

@layer base {
  * {
    box-sizing: border-box;
  }
  
  body {
    @apply bg-space-blue text-white font-cosmic;
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #0B1426 0%, #1E293B 50%, #0F172A 100%);
    background-attachment: fixed;
    overflow-x: hidden;
  }
  
  #root {
    min-height: 100vh;
    width: 100%;
  }
}

@layer components {
  .btn-primary {
    @apply bg-cosmic-purple hover:bg-purple-600 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg;
  }
  
  .btn-secondary {
    @apply bg-star-yellow hover:bg-yellow-400 text-space-blue font-semibold py-3 px-6 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg;
  }
  
  .card {
    @apply bg-slate-800/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6 shadow-xl;
  }
  
  .planet-card {
    @apply card hover:bg-slate-700/50 transition-all duration-300 cursor-pointer transform hover:scale-105;
  }
  
  .star {
    @apply absolute w-1 h-1 bg-white rounded-full animate-twinkle;
  }
}

@layer utilities {
  .text-glow {
    text-shadow: 0 0 10px currentColor;
  }
  
  .bg-stars {
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
  }
}
